<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cricket Commentary - Commentator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .panel-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .panel-content {
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .match-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .match-info h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .match-state {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .state-item {
            text-align: center;
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
        }
        .state-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }
        .state-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .commentary-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .commentary-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .commentary-item:last-child {
            border-bottom: none;
        }
        .ball-info {
            font-weight: bold;
            color: #3498db;
            font-size: 0.9em;
        }
        .commentary-text {
            margin: 5px 0;
            color: #2c3e50;
        }
        .commentary-meta {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        .quick-btn {
            padding: 8px 12px;
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .quick-btn:hover {
            background: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎙️ Commentator Interface</h1>
            <p>Add real-time ball-by-ball commentary</p>
        </div>

        <div class="main-content">
            <!-- Commentary Input Panel -->
            <div class="panel">
                <div class="panel-header">Add Commentary</div>
                <div class="panel-content">
                    <div id="alertContainer"></div>
                    
                    <div class="form-group">
                        <label for="matchSelect">Select Match</label>
                        <select id="matchSelect" onchange="selectMatch()">
                            <option value="">Loading matches...</option>
                        </select>
                    </div>

                    <div id="matchInfo" class="match-info" style="display: none;">
                        <h4 id="matchTitle">Match Details</h4>
                        <div class="match-state">
                            <div class="state-item">
                                <div class="state-value" id="currentOver">0</div>
                                <div class="state-label">Current Over</div>
                            </div>
                            <div class="state-item">
                                <div class="state-value" id="currentBall">0</div>
                                <div class="state-label">Current Ball</div>
                            </div>
                            <div class="state-item">
                                <div class="state-value" id="totalBalls">0</div>
                                <div class="state-label">Total Balls</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="overNumber">Over Number</label>
                            <input type="number" id="overNumber" min="1" max="50" required>
                        </div>
                        <div class="form-group">
                            <label for="ballNumber">Ball Number</label>
                            <input type="number" id="ballNumber" min="1" max="6" required>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <button class="quick-btn" onclick="setNextBall()">Next Ball</button>
                        <button class="quick-btn" onclick="setNewOver()">New Over</button>
                        <button class="quick-btn" onclick="fillSampleText()">Sample Text</button>
                        <button class="quick-btn" onclick="clearForm()">Clear Form</button>
                    </div>

                    <div class="form-group">
                        <label for="commentaryText">Commentary</label>
                        <textarea id="commentaryText" placeholder="Enter ball-by-ball commentary..." required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="commentatorId">Commentator ID</label>
                        <input type="text" id="commentatorId" value="john_doe" required>
                    </div>

                    <button class="btn btn-success" onclick="addCommentary()" id="submitBtn">
                        Add Commentary
                    </button>
                </div>
            </div>

            <!-- Recent Commentary Panel -->
            <div class="panel">
                <div class="panel-header">Recent Commentary</div>
                <div class="panel-content">
                    <div id="recentCommentary" class="commentary-list">
                        <p>Select a match to view recent commentary</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedMatchId = null;
        let currentMatchState = { over: 0, ball: 0, totalBalls: 0 };

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadMatches();
        });

        // Load available matches
        async function loadMatches() {
            try {
                const response = await fetch('/api/matches?status=live&size=20');
                const data = await response.json();
                
                const select = document.getElementById('matchSelect');
                select.innerHTML = '<option value="">Select a match...</option>';
                
                if (data.content && data.content.length > 0) {
                    data.content.forEach(match => {
                        const option = document.createElement('option');
                        option.value = match.id;
                        option.textContent = `${match.team1} vs ${match.team2} (${match.status})`;
                        select.appendChild(option);
                    });
                } else {
                    select.innerHTML = '<option value="">No matches available</option>';
                }
            } catch (error) {
                console.error('Failed to load matches:', error);
                showAlert('Failed to load matches', 'error');
            }
        }

        // Select a match
        async function selectMatch() {
            const matchId = document.getElementById('matchSelect').value;
            if (!matchId) {
                document.getElementById('matchInfo').style.display = 'none';
                document.getElementById('recentCommentary').innerHTML = 
                    '<p>Select a match to view recent commentary</p>';
                return;
            }

            selectedMatchId = matchId;
            
            try {
                // Load match details
                const matchResponse = await fetch(`/api/matches/${matchId}`);
                const match = await matchResponse.json();
                
                // Update UI
                document.getElementById('matchTitle').textContent = 
                    `${match.team1} vs ${match.team2}`;
                document.getElementById('currentOver').textContent = match.currentOver || 0;
                document.getElementById('currentBall').textContent = match.currentBall || 0;
                document.getElementById('totalBalls').textContent = match.totalBalls || 0;
                document.getElementById('matchInfo').style.display = 'block';
                
                // Update current state
                currentMatchState = {
                    over: match.currentOver || 0,
                    ball: match.currentBall || 0,
                    totalBalls: match.totalBalls || 0
                };
                
                // Set next ball automatically
                setNextBall();
                
                // Load recent commentary
                loadRecentCommentary(matchId);
                
            } catch (error) {
                console.error('Failed to load match details:', error);
                showAlert('Failed to load match details', 'error');
            }
        }

        // Load recent commentary
        async function loadRecentCommentary(matchId) {
            try {
                const response = await fetch(`/api/matches/${matchId}/commentary/live`);
                const commentary = await response.json();
                
                const container = document.getElementById('recentCommentary');
                container.innerHTML = '';
                
                if (commentary.length === 0) {
                    container.innerHTML = '<p>No commentary available yet</p>';
                    return;
                }
                
                commentary.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'commentary-item';
                    div.innerHTML = `
                        <div class="ball-info">Over ${item.overNumber}.${item.ballNumber}</div>
                        <div class="commentary-text">${item.commentaryText}</div>
                        <div class="commentary-meta">${item.commentatorId} • ${new Date(item.timestamp).toLocaleTimeString()}</div>
                    `;
                    container.appendChild(div);
                });
                
            } catch (error) {
                console.error('Failed to load recent commentary:', error);
            }
        }

        // Add new commentary
        async function addCommentary() {
            if (!selectedMatchId) {
                showAlert('Please select a match first', 'error');
                return;
            }

            const overNumber = parseInt(document.getElementById('overNumber').value);
            const ballNumber = parseInt(document.getElementById('ballNumber').value);
            const commentaryText = document.getElementById('commentaryText').value.trim();
            const commentatorId = document.getElementById('commentatorId').value.trim();

            if (!overNumber || !ballNumber || !commentaryText || !commentatorId) {
                showAlert('Please fill in all fields', 'error');
                return;
            }

            if (ballNumber < 1 || ballNumber > 6) {
                showAlert('Ball number must be between 1 and 6', 'error');
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Adding...';

            try {
                const response = await fetch('/api/commentary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        matchId: selectedMatchId,
                        overNumber: overNumber,
                        ballNumber: ballNumber,
                        commentaryText: commentaryText,
                        commentatorId: commentatorId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showAlert('Commentary added successfully!', 'success');
                    
                    // Update match state
                    currentMatchState = {
                        over: overNumber,
                        ball: ballNumber,
                        totalBalls: (overNumber - 1) * 6 + ballNumber
                    };
                    
                    // Update UI
                    document.getElementById('currentOver').textContent = overNumber;
                    document.getElementById('currentBall').textContent = ballNumber;
                    document.getElementById('totalBalls').textContent = currentMatchState.totalBalls;
                    
                    // Clear form and set next ball
                    document.getElementById('commentaryText').value = '';
                    setNextBall();
                    
                    // Reload recent commentary
                    loadRecentCommentary(selectedMatchId);
                    
                } else {
                    const error = await response.text();
                    showAlert(`Failed to add commentary: ${error}`, 'error');
                }
                
            } catch (error) {
                console.error('Failed to add commentary:', error);
                showAlert('Failed to add commentary. Please try again.', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Add Commentary';
            }
        }

        // Set next ball automatically
        function setNextBall() {
            let nextOver = currentMatchState.over;
            let nextBall = currentMatchState.ball + 1;
            
            if (nextBall > 6) {
                nextOver += 1;
                nextBall = 1;
            }
            
            if (nextOver === 0) {
                nextOver = 1;
                nextBall = 1;
            }
            
            document.getElementById('overNumber').value = nextOver;
            document.getElementById('ballNumber').value = nextBall;
        }

        // Set new over
        function setNewOver() {
            const nextOver = currentMatchState.over + 1;
            document.getElementById('overNumber').value = nextOver;
            document.getElementById('ballNumber').value = 1;
        }

        // Fill sample commentary text
        function fillSampleText() {
            const sampleTexts = [
                "Good length delivery, defended back to the bowler",
                "Short ball, pulled away for a boundary!",
                "Yorker length, dug out by the batsman",
                "Tossed up delivery, driven through the covers",
                "Quick single taken, good running between the wickets",
                "Appeal for LBW, not given by the umpire"
            ];
            
            const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
            document.getElementById('commentaryText').value = randomText;
        }

        // Clear form
        function clearForm() {
            document.getElementById('commentaryText').value = '';
            document.getElementById('overNumber').value = '';
            document.getElementById('ballNumber').value = '';
        }

        // Show alert message
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            container.innerHTML = '';
            container.appendChild(alert);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
    </script>
</body>
</html>
