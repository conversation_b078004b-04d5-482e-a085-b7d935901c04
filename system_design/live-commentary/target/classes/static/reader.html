<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cricket Commentary - Reader</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        .match-selector {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .match-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        .match-card:hover {
            background-color: #f8f9fa;
            border-color: #3498db;
        }
        .match-card.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .match-card.live {
            border-left: 4px solid #4caf50;
        }
        .match-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
        }
        .match-info {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .live-badge {
            background: #4caf50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .commentary-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .commentary-header {
            background: #3498db;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4caf50;
        }
        .status-indicator.disconnected {
            background: #f44336;
        }
        .commentary-content {
            max-height: 600px;
            overflow-y: auto;
            padding: 20px;
        }
        .commentary-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
            animation: fadeIn 0.5s ease-in;
        }
        .commentary-item:last-child {
            border-bottom: none;
        }
        .commentary-item.new {
            background-color: #fff3cd;
            margin: -15px -20px 15px -20px;
            padding: 15px 20px;
            border-left: 4px solid #ffc107;
        }
        .ball-info {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        .commentary-text {
            color: #2c3e50;
            line-height: 1.6;
        }
        .commentary-meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 8px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏏 Live Cricket Commentary</h1>
            <p>Real-time ball-by-ball updates</p>
        </div>

        <div class="match-selector">
            <h3>Select a Match</h3>
            <div id="matchList" class="loading">Loading matches...</div>
        </div>

        <div class="commentary-section" id="commentarySection" style="display: none;">
            <div class="commentary-header">
                <div>
                    <h3 id="selectedMatchTitle">Match Commentary</h3>
                    <div id="matchProgress"></div>
                </div>
                <div class="connection-status">
                    <span id="connectionText">Connected</span>
                    <div class="status-indicator" id="statusIndicator"></div>
                </div>
            </div>
            <div class="commentary-content" id="commentaryContent">
                <div class="loading">Select a match to view commentary</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <script>
        let stompClient = null;
        let selectedMatchId = null;
        let isConnected = false;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadMatches();
            connectWebSocket();
        });

        // Load available matches
        async function loadMatches() {
            try {
                const response = await fetch('/api/matches?status=live&size=10');
                const data = await response.json();
                
                const matchList = document.getElementById('matchList');
                
                if (data.content && data.content.length > 0) {
                    matchList.innerHTML = '';
                    data.content.forEach(match => {
                        const matchCard = createMatchCard(match);
                        matchList.appendChild(matchCard);
                    });
                } else {
                    matchList.innerHTML = '<p>No live matches available. <a href="#" onclick="loadAllMatches()">View all matches</a></p>';
                }
            } catch (error) {
                console.error('Failed to load matches:', error);
                document.getElementById('matchList').innerHTML = 
                    '<div class="error">Failed to load matches. Please refresh the page.</div>';
            }
        }

        // Load all matches (including upcoming and completed)
        async function loadAllMatches() {
            try {
                const response = await fetch('/api/matches?size=20');
                const data = await response.json();
                
                const matchList = document.getElementById('matchList');
                matchList.innerHTML = '';
                
                data.content.forEach(match => {
                    const matchCard = createMatchCard(match);
                    matchList.appendChild(matchCard);
                });
            } catch (error) {
                console.error('Failed to load all matches:', error);
            }
        }

        // Create match card element
        function createMatchCard(match) {
            const card = document.createElement('div');
            card.className = `match-card ${match.status === 'live' ? 'live' : ''}`;
            card.onclick = () => selectMatch(match);
            
            const title = document.createElement('div');
            title.className = 'match-title';
            title.innerHTML = `${match.team1} vs ${match.team2}`;
            if (match.status === 'live') {
                title.innerHTML += '<span class="live-badge">LIVE</span>';
            }
            
            const info = document.createElement('div');
            info.className = 'match-info';
            info.innerHTML = `${match.venue || 'TBD'} • ${new Date(match.matchDate).toLocaleDateString()}`;
            if (match.currentOver && match.currentBall) {
                info.innerHTML += ` • Over ${match.currentOver}.${match.currentBall}`;
            }
            
            card.appendChild(title);
            card.appendChild(info);
            
            return card;
        }

        // Select a match for commentary viewing
        function selectMatch(match) {
            // Update UI
            document.querySelectorAll('.match-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.match-card').classList.add('selected');
            
            selectedMatchId = match.id;
            document.getElementById('selectedMatchTitle').textContent = 
                `${match.team1} vs ${match.team2}`;
            
            // Show commentary section
            document.getElementById('commentarySection').style.display = 'block';
            
            // Load commentary
            loadCommentary(match.id);
            
            // Subscribe to live updates
            if (stompClient && isConnected) {
                subscribeToMatch(match.id);
            }
        }

        // Load commentary for selected match
        async function loadCommentary(matchId) {
            try {
                document.getElementById('commentaryContent').innerHTML = 
                    '<div class="loading">Loading commentary...</div>';
                
                const response = await fetch(`/api/matches/${matchId}/commentary/live`);
                const commentary = await response.json();
                
                displayCommentary(commentary);
            } catch (error) {
                console.error('Failed to load commentary:', error);
                document.getElementById('commentaryContent').innerHTML = 
                    '<div class="error">Failed to load commentary.</div>';
            }
        }

        // Display commentary in the UI
        function displayCommentary(commentary) {
            const content = document.getElementById('commentaryContent');
            content.innerHTML = '';
            
            if (commentary.length === 0) {
                content.innerHTML = '<div class="loading">No commentary available yet.</div>';
                return;
            }
            
            commentary.forEach(item => {
                const commentaryItem = createCommentaryItem(item);
                content.appendChild(commentaryItem);
            });
            
            // Scroll to top for latest commentary
            content.scrollTop = 0;
        }

        // Create commentary item element
        function createCommentaryItem(commentary) {
            const item = document.createElement('div');
            item.className = 'commentary-item';
            
            const ballInfo = document.createElement('div');
            ballInfo.className = 'ball-info';
            ballInfo.textContent = `Over ${commentary.overNumber}.${commentary.ballNumber}`;
            
            const text = document.createElement('div');
            text.className = 'commentary-text';
            text.textContent = commentary.commentaryText;
            
            const meta = document.createElement('div');
            meta.className = 'commentary-meta';
            meta.textContent = `${commentary.commentatorId} • ${new Date(commentary.timestamp).toLocaleTimeString()}`;
            
            item.appendChild(ballInfo);
            item.appendChild(text);
            item.appendChild(meta);
            
            return item;
        }

        // WebSocket connection management
        function connectWebSocket() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, function(frame) {
                console.log('Connected: ' + frame);
                isConnected = true;
                updateConnectionStatus(true);
                
                if (selectedMatchId) {
                    subscribeToMatch(selectedMatchId);
                }
            }, function(error) {
                console.log('Connection error: ' + error);
                isConnected = false;
                updateConnectionStatus(false);
                
                // Retry connection after 5 seconds
                setTimeout(connectWebSocket, 5000);
            });
        }

        // Subscribe to match updates
        function subscribeToMatch(matchId) {
            if (stompClient && isConnected) {
                stompClient.subscribe(`/topic/match/${matchId}/commentary`, function(message) {
                    const commentary = JSON.parse(message.body);
                    addNewCommentary(commentary);
                });
            }
        }

        // Add new commentary to the top of the list
        function addNewCommentary(commentary) {
            const content = document.getElementById('commentaryContent');
            const newItem = createCommentaryItem(commentary);
            newItem.classList.add('new');
            
            // Remove loading message if present
            const loading = content.querySelector('.loading');
            if (loading) {
                loading.remove();
            }
            
            // Add to top
            content.insertBefore(newItem, content.firstChild);
            
            // Remove 'new' class after animation
            setTimeout(() => {
                newItem.classList.remove('new');
            }, 2000);
            
            // Limit to 50 items for performance
            const items = content.querySelectorAll('.commentary-item');
            if (items.length > 50) {
                items[items.length - 1].remove();
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('connectionText');
            
            if (connected) {
                indicator.classList.remove('disconnected');
                text.textContent = 'Connected';
            } else {
                indicator.classList.add('disconnected');
                text.textContent = 'Disconnected';
            }
        }

        // Refresh matches every 30 seconds
        setInterval(loadMatches, 30000);
    </script>
</body>
</html>
